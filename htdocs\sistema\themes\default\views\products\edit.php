<?php (defined('BASEPATH')) OR exit('No direct script access allowed'); ?>

<section class="content">
    <div class="row">
        <div class="col-xs-12">
            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title"><?= lang('update_info'); ?></h3>
                </div>
                <div class="box-body">
                    <div class="col-lg-12">
                        <?= form_open_multipart("products/edit/".$product->id, 'class="validation"');?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group" style="display: none;">
                                    <?= lang('type', 'type'); ?>
                                    <?php $opts = array('standard' => "Produto", 'service' => lang('service')); ?>
                                    <?= form_dropdown('type', $opts, set_value('type', $product->type), 'class="form-control tip select2" id="type"  required="required" style="width:100%;"'); ?>
                                </div>

                                <div class="form-group">
                                    <?= lang('code', 'code'); ?>
                                    <?= form_input('code', $product->code, 'class="form-control tip" id="code" maxlength="50" required="required"'); ?>
                                </div>

                                <div class="form-group">
                                    <?= lang('name', 'name'); ?>
                                    <?= form_input('name', $product->name, 'class="form-control tip" id="name" maxlength="120" required="required"'); ?>
                                </div>

                                <div class="form-group">
                                    <?= lang('cost', 'cost'); ?>
                                    <?= form_input('cost', $product->cost, 'class="form-control tip dinheiroinput" id="cost"  required="required"'); ?>
                                </div>

                                <div class="form-group">
                                    <?= lang('Margem (%)', 'margem'); ?>
                                    <?= form_input('margem', (($product->cost>0 && $product->price>0)?(number_format((($product->price-$product->cost)/$product->price)*100, 2, ',', '')):""),  'class="form-control tip justnum" id="margem"'); ?>
                                </div>

                                <div class="form-group">
                                    <?= lang('price', 'price'); ?>
                                    <?= form_input('price', $product->price, 'class="form-control tip dinheiroinput" id="price"  required="required"'); ?>
                                </div>

                                <div class="form-group">
                                    <?= lang('Quantidade em estoque', 'Quantidade em estoque'); ?>
                                    <?= form_input('quantity', number_format($product->quantity, 0, '', ''), 'class="form-control tip" id="quantity" required="required"'); ?>
                                </div>

                                <div class="form-group">
                                    <?= lang('category', 'category'); ?>
                                    <?php
                                    $cat[''] = lang("select")." ".lang("category");
                                    foreach($categories as $category) {
                                        $cat[$category->id] = $category->name;
                                    }
                                    ?>
                                    <?= form_dropdown('category', $cat, $product->category_id, 'class="form-control select2 tip" id="category"  required="required" style="width:100%;"'); ?>
                                </div>

                                <div class="form-group">
                                    <label for="validade">Validade (mm/aa)</label>
                                    <?= form_input('validade', $product->validade, 'class="form-control tip" id="validade" placeholder="mm/aa"'); ?>
                                </div>

                                <div class="form-group">
                                    <label for="cest">Código CEST</label>
                                    <?= form_input('cest', $product->cest, 'class="form-control tip" id="cest"'); ?>
                                </div>

                                <div class="form-group">
                                    <label for="ncm">Código NCM</label>
                                    <?= form_input('ncm', $product->ncm, 'class="form-control tip"  id="ncm"'. (($Settings->ativar_emissao_notas=="1")));  ?>
                                </div>

                                <div class="form-group">
                                    <input type="file" name="userfile" id="image" style="display:none" accept="image/*">
                                    <div class="image-preview" style="width:150px; height:150px; border:1px solid #ccc; margin-bottom:10px; display:flex; align-items:center; justify-content:center; cursor:pointer" onclick="$('#image').click()">
                                        <img id="preview" src="<?= $product->image ? base_url('uploads/' . $product->image) : base_url('uploads/no_image.png') ?>" style="max-width:100%; max-height:100%;">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <?= lang('details', 'details'); ?>
                                    <?= form_textarea('details', $product->details, 'class="form-control tip redactor" id="details"'); ?>
                                </div>

                                <div class="form-group">
                                    <?= form_submit('edit_product', lang('edit_product'), 'class="btn btn-primary"'); ?>
                                </div>

                                <input name='product_tax' type="hidden" value='0' id="product_tax">
                                <input name='tax_method'  type="hidden"  value='1' id="tax_method">
                                <input name='unit'  type="hidden"  value='UN' id="unit">
                                <input name='alert_quantity'  type="hidden"  value='0' id="alert_quantity">
                                <input name='cfop2'  type="hidden"  value='6405' id="cfop2">
                            </div>
                            <div class="col-md-6" id="tax_column" style="display: none;">
                                <h3>Configuração de Impostos <span style="font-size:14px;"> (<?php echo (($Settings->ativar_emissao_notas=="1")?'Obrigatório':'Não Obrigatório'); ?>)</span></h3>
                              <div class="form-group st">
                                <label for="origem">Origem do produto</label>
                                    <?php $tm = array(0 => '0 - Nacional, exceto as indicadas nos códigos 3, 4, 5 e 8', 1 => '1 - Estrangeira - Importação direta, exceto a indicada no código 6', 2 => '2 - Estrangeira - Adquirida no mercado interno, exceto a indicada no código 7', 3 => '3 - Nacional, mercadoria ou bem com Conteúdo de Importação superior a 40% e inferior ou igual a 70%', 4 => '4 - Nacional, cuja produção tenha sido feita em conformidade com os processos produtivos básicos de que tratam as legislações citadas nos Ajustes', 5 => '5 - Nacional, mercadoria ou bem com Conteúdo de Importação inferior ou igual a 40%', 6 => '6 - Estrangeira - Importação direta, sem similar nacional, constante em lista da CAMEX e gás natural', 7 => '7 - Estrangeira - Adquirida no mercado interno, sem similar nacional, constante lista CAMEX e gás natural', 8 => '8 - Nacional, mercadoria ou bem com Conteúdo de Importação superior a 70%'); ?>
                                    <?= form_dropdown('origem', $tm, $product->origem, 'class="form-control tip select2" id="origem" style="width:100%;"'. (($Settings->ativar_emissao_notas=="1")?' required="required"':''));  ?>
                                </div>
                                <div class="form-group">
                                <label for="cfop">CFOP (Dentro do Estado)</label>
                                    <?= form_dropdown('cfop', listaCFOPPRODUTOS, $product->cfop, 'class="form-control tip select2" id="cfop"  style="width:100%;"'. (($Settings->ativar_emissao_notas=="1")?' required="required"':''));  ?>
                                </div>

                                <div class="form-group">
                                   <label for="impostos">Grupo de Impostos</label>
                                    <?php
                                    $imp[''] = lang("select");
                                    foreach($impostos as $imposto) {
                                        if($imposto->tipo==1){ $impadd = "Produto - "; }else{ $impadd = "Serviço - "; }
                                        $imp[$imposto->id] = $impadd.$imposto->nome;
                                    }
                                    ?>
                                    <?= form_dropdown('imposto', $imp, $product->imposto, 'class="form-control select2 tip" id="impostos" style="width:100%;"'. (($Settings->ativar_emissao_notas=="1")?' required="required"':''));  ?>
                                </div>
                            </div>
                        </div>
                        <?= form_close();?>
                    </div>
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>

        <!-- Histórico de Alterações -->
        <?php if (!empty($audit_log)): ?>
        <div class="col-xs-12">
            <div class="box box-info">
                <div class="box-header">
                    <h3 class="box-title">Histórico de Alterações</h3>
                </div>
                <div class="box-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th style="text-align: center;">Campo Alterado</th>
                                    <th style="text-align: center;">Detalhes da Alteração</th>
                                    <th style="text-align: center;">Data/Hora</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($audit_log as $log): ?>
                                <tr>
                                    <td style="text-align: center;"><strong><?= $log->field_name ?></strong></td>
                                    <td style="text-align: center;">
                                        <span class="text-muted">Valor anterior:</span> <strong><?= $log->old_value ?></strong><br>
                                        <span class="text-muted">Valor atual:</span> <strong><?= $log->new_value ?></strong><br>
                                        <span class="text-muted">Alteração por:</span> <strong><?= $log->username ?></strong>
                                    </td>
                                    <td style="text-align: center;"><?= date('d/m/Y - H:i', strtotime($log->created_at)) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<script src="<?= $assets ?>dist/js/jquery-ui.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/jquery.mask.js" type="text/javascript"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $('.dinheiroinput').mask("#.##0,00", {reverse: true});
        $('#alert_quantity').mask("00000");
        $('.justnum').mask("##0,00", {reverse: true});
        $('#validade').mask("00/00");

        // Função para converter valor em dinheiro para número
        function convertMoneyToNumber(value) {
            return parseFloat(value.replace(/\./g, '').replace(',', '.')) || 0;
        }

        // Função para calcular a margem bruta
        function calculateMargin() {
            var cost = convertMoneyToNumber($('#cost').val());
            var price = convertMoneyToNumber($('#price').val());

            if (cost > 0 && price > 0) {
                var margin = ((price - cost) / price) * 100;
                $('#margem').val(margin.toFixed(2).replace('.', ','));
            }
        }

        // Função para calcular o preço baseado na margem bruta
        function calculatePrice() {
            var cost = convertMoneyToNumber($('#cost').val());
            var margin = parseFloat($('#margem').val().replace(',', '.')) || 0;

            if (cost > 0 && margin < 100) {
                var price = cost / (1 - (margin / 100));
                $('#price').val(price.toFixed(2).replace('.', ','));
            }
        }

        // Quando o custo é alterado
        $('#cost').keyup(function() {
            if ($('#margem').val()) {
                calculatePrice();
            } else {
                calculateMargin();
            }
        });

        // Quando o preço é alterado
        $('#price').keyup(function() {
            calculateMargin();
        });

        // Quando a margem é alterada
        $('#margem').keyup(function() {
            calculatePrice();
        });

        // Validação simples para quantidade (apenas números)
        $('#quantity').on('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // Ocultar coluna de impostos por padrão
        $('#tax_column').hide();

        // Set default values for CFOP, CEST and tax group
        if (!$('#cfop').val()) {
            $('#cfop').val('5405').trigger('change');
        }

        // Find and select the tax group with CSOSN 500
        if (!$('#impostos').val()) {
            $("#impostos option").each(function() {
                if ($(this).text().includes("SIMPLES CST 500")) {
                    $('#impostos').val($(this).val()).trigger('change');
                    return false; // Stop the loop once found
                }
            });

            // If no tax group with CSOSN 500 was found, try to select it by ID 3
            if (!$('#impostos').val() || $('#impostos').val() === '') {
                $('#impostos').val('3').trigger('change');
            }
        }

        // Image preview
        function readURL(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#preview').attr('src', e.target.result);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

        $("#image").change(function() {
            readURL(this);
        });

        // Formatação do campo validade
        $('#validade').on('input', function() {
            var val = $(this).val().replace(/\D/g, '');
            if (val.length > 4) val = val.substring(0, 4);
            var newVal = '';

            if (val.length > 0) {
                newVal = val.substring(0, 2);
                if (val.length > 2) {
                    newVal += '/' + val.substring(2);
                }
            }
            $(this).val(newVal);
        });

        // Remove a formatação duplicada no evento blur
        $('#validade').off('blur');

        // Formatação do campo CEST
        $('#cest').on('blur', function() {
            var cest = $(this).val().replace(/\D/g, ''); // Remove caracteres não numéricos
            if (cest.length > 0) {
                // Formata como XX.XXX.XX
                if (cest.length <= 2) {
                    cest = cest;
                } else if (cest.length <= 5) {
                    cest = cest.substring(0, 2) + '.' + cest.substring(2);
                } else {
                    cest = cest.substring(0, 2) + '.' + cest.substring(2, 5) + '.' + cest.substring(5, 7);
                }
                $(this).val(cest);
            }
        });

        // Verificar o valor do campo validade antes de enviar o formulário
        $("form").on('submit', function(e) {
            console.log("Valor do campo validade antes de enviar: " + $("#validade").val());
            // Armazenar o valor em um cookie para verificação posterior
            document.cookie = "validade_enviada=" + $("#validade").val() + "; path=/";
            // Verificar o valor do campo CEST antes de enviar
            console.log("Valor do campo CEST antes de enviar: " + $("#cest").val());
            document.cookie = "cest_enviado=" + $("#cest").val() + "; path=/";
        });
    });
</script>