/**
 * Force Click-Only Sidebar Behavior
 * 
 * This script completely overrides any hover behavior on the sidebar
 * and ensures it only responds to clicks.
 */
$(document).ready(function() {
    
    // Wait for AdminLTE to load completely
    setTimeout(function() {
        
        // Completely disable expandOnHover functionality
        if (typeof $.AdminLTE !== 'undefined' && $.AdminLTE.pushMenu) {
            $.AdminLTE.pushMenu.expandOnHover = function() {
                // Completely disabled - do nothing
                return false;
            };
            
            // Override the expand and collapse functions to prevent hover-triggered changes
            var originalExpand = $.AdminLTE.pushMenu.expand;
            var originalCollapse = $.AdminLTE.pushMenu.collapse;
            
            $.AdminLTE.pushMenu.expand = function() {
                // Only allow expansion if it's not triggered by hover
                if (!$('body').hasClass('sidebar-expanded-on-hover')) {
                    originalExpand.call(this);
                }
            };
            
            $.AdminLTE.pushMenu.collapse = function() {
                // Only allow collapse if it's not triggered by hover
                if (!$('body').hasClass('sidebar-expanded-on-hover')) {
                    originalCollapse.call(this);
                }
            };
        }
        
        // Remove all existing hover event handlers from sidebar
        $('.main-sidebar').off('mouseenter.pushMenu mouseleave.pushMenu');
        $('.main-sidebar').off('mouseenter mouseleave hover');
        $('.main-sidebar *').off('mouseenter mouseleave hover');
        
        // Prevent any new hover handlers from being attached
        var originalOn = $.fn.on;
        $.fn.on = function(events, selector, data, handler) {
            // If this is trying to attach hover events to the sidebar, block it
            if (this.hasClass('main-sidebar') || this.closest('.main-sidebar').length) {
                if (typeof events === 'string' && (events.indexOf('mouseenter') !== -1 || events.indexOf('mouseleave') !== -1 || events.indexOf('hover') !== -1)) {
                    console.log('Blocked hover event attachment to sidebar:', events);
                    return this;
                }
            }
            return originalOn.call(this, events, selector, data, handler);
        };
        
        // Override hover function for sidebar elements
        var originalHover = $.fn.hover;
        $.fn.hover = function(handlerIn, handlerOut) {
            // If this is the sidebar or its children, don't attach hover
            if (this.hasClass('main-sidebar') || this.closest('.main-sidebar').length) {
                console.log('Blocked hover function call on sidebar element');
                return this;
            }
            return originalHover.call(this, handlerIn, handlerOut);
        };
        
        // Force sidebar to stay in its current state
        $(document).on('mouseenter mouseleave', '.main-sidebar', function(e) {
            e.stopImmediatePropagation();
            e.preventDefault();
            return false;
        });
        
        // Ensure the sidebar options are set correctly
        if (typeof $.AdminLTE !== 'undefined' && $.AdminLTE.options) {
            $.AdminLTE.options.sidebarExpandOnHover = false;
        }
        
        console.log('Sidebar hover behavior completely disabled - click-only mode active');
        
    }, 1000); // Wait 1 second for AdminLTE to fully initialize
    
});

// Also run when the page is fully loaded
$(window).on('load', function() {
    // Double-check that hover is disabled
    $('.main-sidebar').off('mouseenter mouseleave hover');
    
    // Remove any classes that might trigger hover behavior
    $('body').removeClass('sidebar-expanded-on-hover');
});
