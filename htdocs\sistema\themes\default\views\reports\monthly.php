<style>
.table td { width: 8.333%; }
.table tr:first-child td { text-align:center; }
.table tr:last-child td { text-align:right; }
</style>
<section class="content">
    <div class="row">
        <div class="col-xs-12">
            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title"><span class='text-warning'>1) <?=$this->lang->line('tax')?> = <?= lang('orange'); ?></span> / 2) <?=$this->lang->line('discount')?> = <?= lang('grey'); ?> / <span class='text-success'>3) <?=$this->lang->line('total')?> de vendas = <?= lang('green'); ?></span> / 4) Total Geral</h3>
                </div>
                <div class="box-body">
                    <div class="col-sm-12 noprint" style="text-align:right;margin-bottom:10px;">
                        <button type="button" onclick="window.print()" class="btn btn-default btn-sm noprint">Imprimir</button>
                    </div>

                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-md-3 col-sm-6 col-xs-12">
                                <div class="info-box bg-aqua">
                                    <span class="info-box-icon"><i class="fa fa-shopping-cart"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= lang('sales_value'); ?></span>
                                        <span class="info-box-number"><?= $this->tec->formatMoney($total_sales->total_amount) ?></span>
                                        <div class="progress">
                                            <div style="width: 100%" class="progress-bar"></div>
                                        </div>
                                        <span class="progress-description">
                                            <?= $total_sales->total .' ' . lang('sales'); ?> |
                                            <?= $this->tec->formatMoney($total_sales->paid) . ' ' . lang('received') ?> |
                                            <?= $this->tec->formatMoney($total_sales->tax) . ' ' . lang('tax') ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-12">
                                <div class="info-box bg-yellow">
                                    <span class="info-box-icon"><i class="fa fa-plus"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= lang('purchases_value'); ?></span>
                                        <span class="info-box-number"><?= $this->tec->formatMoney($total_purchases->total_amount) ?></span>
                                        <div class="progress">
                                            <div style="width: 0%" class="progress-bar"></div>
                                        </div>
                                        <span class="progress-description">
                                            <?= $total_purchases->total ?> <?= lang('purchases'); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-12">
                                <div class="info-box bg-red">
                                    <span class="info-box-icon"><i class="fa fa-circle-o"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= lang('expenses_value'); ?></span>
                                        <span class="info-box-number"><?= $this->tec->formatMoney($total_expenses->total_amount) ?></span>
                                        <div class="progress">
                                            <div style="width: 0%" class="progress-bar"></div>
                                        </div>
                                        <span class="progress-description">
                                            <?= $total_expenses->total ?> <?= lang('expenses'); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-12">
                                <div class="info-box bg-green">
                                    <span class="info-box-icon"><i class="fa fa-dollar"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= lang('profit_loss'); ?></span>
                                        <span class="info-box-number"><?= $this->tec->formatMoney($total_sales->total_amount-$total_purchases->total_amount-$total_expenses->total_amount) ?></span>
                                        <div class="progress">
                                            <div style="width: 100%" class="progress-bar"></div>
                                        </div>
                                        <span class="progress-description">
                                            <?= $total_sales->total_amount.' - '.$total_purchases->total_amount.' - '.$total_expenses->total_amount;?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                        <div class="table-responsive" style="margin-top: 20px;">
                            <h4><?= lang('Demonstração do Resultado'); ?></h4>
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th><?= lang('description'); ?></th>
                                        <th><?= lang('cal_january'); ?></th>
                                        <th><?= lang('cal_february'); ?></th>
                                        <th><?= lang('cal_march'); ?></th>
                                        <th><?= lang('cal_april'); ?></th>
                                        <th><?= lang('cal_may'); ?></th>
                                        <th><?= lang('cal_june'); ?></th>
                                        <th><?= lang('cal_july'); ?></th>
                                        <th><?= lang('cal_august'); ?></th>
                                        <th><?= lang('cal_september'); ?></th>
                                        <th><?= lang('cal_october'); ?></th>
                                        <th><?= lang('cal_november'); ?></th>
                                        <th><?= lang('cal_december'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #ffffff;">
                                        <td>Total Vendas:</td>
                                        <?php
                                        $monthly_totals = array_fill(1, 12, 0.00);
                                        $monthly_discounts = array_fill(1, 12, 0.00);
                                        $monthly_costs = array_fill(1, 12, 0.00);
                                        $monthly_expenses = array_fill(1, 12, 0.00);
                                        
                                        if(!empty($sales)) {
                                            foreach($sales as $value) {
                                                $monthly_totals[$value->date] = $value->total;
                                                $monthly_discounts[$value->date] = $value->discount;
                                            }
                                        }
                                        
                                        // Fetch product costs from sales items
                                        if(!empty($sale_items)) {
                                            foreach($sale_items as $item) {
                                                $month = date('n', strtotime($item->date));
                                                $monthly_costs[$month] += ($item->cost * $item->quantity);
                                            }
                                        }
                                        
                                        // Fetch expenses
                                        if(!empty($expenses)) {
                                            foreach($expenses as $expense) {
                                                $month = isset($expense->month) ? $expense->month : date('n', strtotime($expense->date));
                                                $monthly_expenses[$month] += $expense->amount;
                                            }
                                        }
                                        
                                        for ($i = 1; $i <= 12; $i++){
                                            echo "<td class='text-right'>".$this->tec->formatMoney($monthly_totals[$i])."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <?php
                                    // Processar dados de pagamentos mensais
                                    $monthly_debito = array_fill(1, 12, 0.00);
                                    $monthly_credito = array_fill(1, 12, 0.00);
                                    $monthly_dinheiro = array_fill(1, 12, 0.00);
                                    $monthly_pix = array_fill(1, 12, 0.00);

                                    if(!empty($monthly_payments)) {
                                        foreach($monthly_payments as $payment) {
                                            $month = (int)$payment->month;
                                            switch($payment->paid_by) {
                                                case 'stripe':
                                                    $monthly_debito[$month] += $payment->total_amount;
                                                    break;
                                                case 'CC':
                                                    $monthly_credito[$month] += $payment->total_amount;
                                                    break;
                                                case 'cash':
                                                    $monthly_dinheiro[$month] += $payment->total_amount;
                                                    break;
                                                case 'pix':
                                                    $monthly_pix[$month] += $payment->total_amount;
                                                    break;
                                            }
                                        }
                                    }
                                    ?>
                                    <tr style="background-color: #f5f5f5;">
                                        <td style="padding-left: 20px; font-style: italic;">Débito:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            echo "<td class='text-right'>".$this->tec->formatMoney($monthly_debito[$i])."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #ffffff;">
                                        <td style="padding-left: 20px; font-style: italic;">Crédito:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            echo "<td class='text-right'>".$this->tec->formatMoney($monthly_credito[$i])."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #f5f5f5;">
                                        <td style="padding-left: 20px; font-style: italic;">Dinheiro:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            echo "<td class='text-right'>".$this->tec->formatMoney($monthly_dinheiro[$i])."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #ffffff;">
                                        <td style="padding-left: 20px; font-style: italic;">PIX:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            echo "<td class='text-right'>".$this->tec->formatMoney($monthly_pix[$i])."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #f5f5f5;">
                                        <td>Descontos:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            echo "<td class='text-right'>".$this->tec->formatMoney($monthly_discounts[$i])."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #ffffff;">
                                        <td>Vendas Líquidas:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            $net_sales = $monthly_totals[$i] - $monthly_discounts[$i];
                                            echo "<td class='text-right'>".$this->tec->formatMoney($net_sales)."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #f5f5f5;">
                                        <td>Custo Produtos:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            echo "<td class='text-right'>".$this->tec->formatMoney($monthly_costs[$i])."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #ffffff;">
                                        <td>Lucro Bruto:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            $net_sales = $monthly_totals[$i] - $monthly_discounts[$i];
                                            $gross_profit = $net_sales - $monthly_costs[$i];
                                            echo "<td class='text-right'>".$this->tec->formatMoney($gross_profit)."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #f5f5f5;">
                                        <td>Despesas do Período:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            echo "<td class='text-right'>".$this->tec->formatMoney($monthly_expenses[$i])."</td>";
                                        }
                                        ?>
                                    </tr>
                                    <tr style="background-color: #ffffff;">
                                        <td>Lucro Líquido:</td>
                                        <?php
                                        for ($i = 1; $i <= 12; $i++){
                                            $net_sales = $monthly_totals[$i] - $monthly_discounts[$i];
                                            $gross_profit = $net_sales - $monthly_costs[$i];
                                            $net_profit = $gross_profit - $monthly_expenses[$i];
                                            echo "<td class='text-right'>".$this->tec->formatMoney($net_profit)."</td>";
                                        }
                                        ?>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
