<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Products_model extends CI_Model
{


    public function __construct() {
        parent::__construct();

    }

    public function getLastProdCode() {
        $this->db->select('code')
        ->limit(1, 0)->order_by("code", "desc");
        $q = $this->db->get('products');
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return false;
    }

    public function getAllProducts() {
        $q = $this->db->get('products');
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return false;
    }

    public function products_count($category_id = NULL) {
        if ($category_id) {
            $this->db->where('category_id', $category_id);
            return $this->db->count_all_results("products");
        } else {
            return $this->db->count_all("products");
        }
    }

    public function fetch_products($limit, $start, $category_id = NULL) {
        $this->db->select('name, code, barcode_symbology, price')
        ->limit($limit, $start)->order_by("code", "asc");
        if ($category_id) {
            $this->db->where('category_id', $category_id);
        }
        $q = $this->db->get("products");

        if ($q->num_rows() > 0) {
            foreach ($q->result() as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return false;
    }


	public function getMunicipio($code) {

		$this->db->select('codigo, nome');
		$this->db->from('municipios');
		$this->db->like('uf', $code);
		$q = $this->db->get();

        if ($q->num_rows() > 0) {
            return $q->result();
        }
        return FALSE;
    }


    public function insertImpostos($data) {

        if ($this->db->insert('impostos', $data)) {
            $id = $this->db->insert_id();
            return $id;
        }
        return false;
    }

    public function updateImpostos($id, $data) {

        if ($this->db->update('impostos', $data, array('id' => $id))) {
            
            return true;
        }
        return false;
    }

    public function getImpostos($id = "") {
        
        if($id!=""){
            $q = $this->db->get_where('impostos', array('id' => $id), 1);
            if ($q->num_rows() > 0) {
                return $q->row();
            }
        }else{

            $this->db->order_by('nome');
            $q = $this->db->get('impostos');
            if ($q->num_rows() > 0) {
                foreach (($q->result()) as $row) {
                    $data[] = $row;
                }
                return $data;
            }
        }

        return FALSE;
    }

    public function getImpostosbyNome($nome) {
        
            $q = $this->db->get_where('impostos', array('nome' => $nome), 1);
            if ($q->num_rows() > 0) {
                return TRUE;
            }

        return FALSE;
    }



    public function getProductByCode($code) {
        $q = $this->db->get_where('products', array('code' => $code), 1);
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

    public function addProduct($data, $items = array()) {
        // Depuração para verificar os dados recebidos para adicionar o produto
        error_log('Dados recebidos para adicionar produto: ' . print_r($data, true));
        error_log('Campo CEST recebido: ' . (isset($data['cest']) ? $data['cest'] : 'não definido'));
        
        // Verificar se o campo CEST está presente nos dados
        if (isset($data['cest'])) {
            error_log('Valor do campo CEST antes da inserção: ' . $data['cest']);
        } else {
            error_log('Campo CEST não está presente nos dados');
        }
        
        // Ensure validade field is properly formatted
        if (isset($data['validade']) && !empty($data['validade'])) {
            $data['validade'] = trim($data['validade']);
            // Convert mm/aa format to YYYY-MM-DD format
            if (preg_match('/^(\d{2})\/(\d{2})$/', $data['validade'], $matches)) {
                $month = $matches[1];
                $year = '20' . $matches[2];
                // Set to last day of the month
                $data['validade'] = date('Y-m-d', strtotime($year . '-' . $month . '-01 +1 month -1 day'));
            }
        }
        
        if ($this->db->insert('products', $data)) {
            $product_id = $this->db->insert_id();
            
            // Depuração para verificar o produto após a inserção
            $produto_inserido = $this->db->get_where('products', array('id' => $product_id), 1)->row();
            error_log('Produto após inserção: ' . print_r($produto_inserido, true));
            error_log('CEST após inserção: ' . $produto_inserido->cest);
            error_log('Validade após inserção: ' . $produto_inserido->validade);
            
            if(! empty($items)) {
                foreach ($items as $item) {
                    $item['product_id'] = $product_id;
                    $this->db->insert('combo_items', $item);
                }
            }
            return true;
        }
        return false;
    }

    public function add_products($data = array()) {
        if ($this->db->insert_batch('products', $data)) {
            return true;
        }
        return false;
    }

    public function updatePrice($data = array()) {
        if ($this->db->update_batch('products', $data, 'code')) {
            return true;
        }
        return false;
    }

    public function logProductChange($product_id, $field_name, $old_value, $new_value, $user_id, $username) {
        // Só registra se os valores forem diferentes
        if ($old_value != $new_value) {
            $audit_data = array(
                'product_id' => $product_id,
                'field_name' => $field_name,
                'old_value' => $old_value,
                'new_value' => $new_value,
                'user_id' => $user_id,
                'username' => $username
            );
            $this->db->insert('product_audit_log', $audit_data);
        }
    }

    public function getProductAuditLog($product_id) {
        $this->db->select('*');
        $this->db->from('product_audit_log');
        $this->db->where('product_id', $product_id);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit(5); // Limitar a apenas as últimas 5 alterações
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->result();
        }
        return FALSE;
    }

    public function updateProduct($id, $data = array(), $items = array(), $photo = NULL) {
        if ($photo) { $data['image'] = $photo; }

        // Ensure validade field is properly formatted
        if (isset($data['validade']) && !empty($data['validade'])) {
            $data['validade'] = trim($data['validade']);
            // Convert mm/aa format to YYYY-MM-DD format
            if (preg_match('/^(\d{2})\/(\d{2})$/', $data['validade'], $matches)) {
                $month = $matches[1];
                $year = '20' . $matches[2];
                // Set to last day of the month
                $data['validade'] = date('Y-m-d', strtotime($year . '-' . $month . '-01 +1 month -1 day'));
            }
        }
        
        if ($this->db->update('products', $data, array('id' => $id))) {
            if(! empty($items)) {
                $this->db->delete('combo_items', array('product_id' => $id));
                foreach ($items as $item) {
                    $item['product_id'] = $id;
                    $this->db->insert('combo_items', $item);
                }
            }
            return true;
        }
        return false;
    }

    public function getComboItemsByPID($product_id) {
        $this->db->select($this->db->dbprefix('products') . '.id as id, ' . $this->db->dbprefix('products') . '.code as code, ' . $this->db->dbprefix('combo_items') . '.quantity as qty, ' . $this->db->dbprefix('products') . '.name as name')
        ->join('products', 'products.code=combo_items.item_code', 'left')
        ->group_by('combo_items.id');
        $q = $this->db->get_where('combo_items', array('product_id' => $product_id));
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

    public function deleteProduct($id) {
        if ($this->db->delete('products', array('id' => $id))) {
            return true;
        }
        return FALSE;
    }

    public function getProductNames($term, $limit = 10) {
        $this->db->where("type != 'combo' AND (name LIKE '%" . $term . "%' OR code LIKE '%" . $term . "%' OR  concat(name, ' (', code, ')') LIKE '%" . $term . "%')");
        $this->db->limit($limit);
        $q = $this->db->get('products');
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

}