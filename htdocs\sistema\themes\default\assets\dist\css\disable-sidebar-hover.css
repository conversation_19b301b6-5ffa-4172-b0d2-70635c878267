/**
 * Disable Sidebar Hover Effects
 * 
 * This CSS file removes all hover effects from the sidebar
 * to ensure it only responds to clicks.
 */

/* Disable hover effects on main sidebar */
.main-sidebar:hover,
.main-sidebar:hover *,
.left-side:hover,
.left-side:hover * {
    /* Remove any hover-specific styles */
    transition: none !important;
    transform: none !important;
    width: inherit !important;
}

/* Prevent sidebar expansion on hover */
.sidebar-mini.sidebar-collapse .main-sidebar:hover {
    width: 50px !important;
    transform: translate(0, 0) !important;
}

/* Disable hover effects on sidebar menu items */
.sidebar-menu li:hover,
.sidebar-menu li a:hover {
    /* Keep original styles, don't change on hover */
    background-color: inherit !important;
    color: inherit !important;
}

/* Disable hover effects on treeview items */
.sidebar-menu .treeview:hover > a,
.sidebar-menu .treeview:hover > a > span {
    background-color: inherit !important;
    color: inherit !important;
}

/* Ensure sidebar stays collapsed when hovered */
.sidebar-mini.sidebar-collapse .main-sidebar:hover {
    width: 50px !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar:hover .sidebar-menu > li > a > span {
    display: none !important;
}

/* Disable any transition effects that might be triggered by hover */
.main-sidebar,
.main-sidebar *,
.left-side,
.left-side * {
    transition: none !important;
}

/* Force sidebar to maintain its state */
.sidebar-mini.sidebar-collapse .main-sidebar {
    width: 50px !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar .sidebar-menu > li > a > span {
    display: none !important;
}

/* Disable pointer events on sidebar elements that might trigger hover */
.main-sidebar .sidebar-menu > li > a > span {
    pointer-events: none !important;
}

/* Re-enable pointer events only for clickable elements */
.main-sidebar .sidebar-menu > li > a {
    pointer-events: auto !important;
}

/* Ensure floating menus don't appear on hover */
.floating-menu {
    display: none !important;
}

.floating-menu.show {
    display: block !important;
}

/* Remove any cursor changes that might indicate hover */
.main-sidebar .sidebar-menu > li > a {
    cursor: pointer !important;
}

/* Disable any animations that might be triggered by hover */
@keyframes none {
    0% { opacity: 1; }
    100% { opacity: 1; }
}

.main-sidebar *,
.left-side * {
    animation: none !important;
}
