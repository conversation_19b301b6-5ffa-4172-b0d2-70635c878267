/**
 * Disable sidebar expand on hover functionality
 */
$(function() {
    // Override the AdminLTE options to disable sidebar expand on hover
    var AdminLTEOptions = {
        sidebarExpandOnHover: false
    };

    // Remove ALL hover event handlers from main-sidebar and its children
    $('.main-sidebar').off('mouseenter mouseleave hover');
    $('.main-sidebar *').off('mouseenter mouseleave hover');

    // Disable the expandOnHover functionality completely
    if ($.AdminLTE && $.AdminLTE.pushMenu) {
        $.AdminLTE.pushMenu.expandOnHover = function() {
            // Do nothing - completely disable expand on hover
        };
    }

    // Ensure sidebar stays in its current state when mouse hovers over it
    $('.main-sidebar').hover(function() {
        // Do nothing on hover to prevent auto-expansion
    });

    // Also disable any hover effects that might be added later
    $(document).on('mouseenter mouseleave', '.main-sidebar', function(e) {
        e.stopPropagation();
        e.preventDefault();
        return false;
    });
});