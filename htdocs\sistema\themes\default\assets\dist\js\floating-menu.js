/**
 * Floating Menu Functionality
 *
 * This script adds floating menus to the sidebar that appear when clicking
 * on the main icons when the sidebar is collapsed.
 * HOVER FUNCTIONALITY DISABLED - Only responds to clicks now.
 */
$(function() {
    // Only apply this functionality when the sidebar is collapsed
    function initFloatingMenus() {
        // Get all treeview menu items in the sidebar
        var treeviewItems = $('.sidebar-menu > li.treeview');

        // For each treeview menu item
        treeviewItems.each(function() {
            var $this = $(this);
            var $link = $this.find('> a');
            var $submenu = $this.find('> ul.treeview-menu');

            // Create a floating menu container for this item
            var menuId = 'floating-menu-' + $this.attr('class').replace(/\s+/g, '-');
            var $floatingMenu = $('<div class="floating-menu" id="' + menuId + '"></div>');

            // Check if this is a treeview item without a submenu (like Settings)
            if ($submenu.length === 0) {
                // This is a treeview item without submenu, create a simple tooltip instead
                var linkText = $link.find('span').text();
                var linkHref = $link.attr('href');
                var $tooltipContent = $('<ul><li><a href="' + linkHref + '"><i class="fa fa-circle-o"></i> ' + linkText + '</a></li></ul>');
                $floatingMenu.append($tooltipContent);
            } else {
                // Clone the submenu into the floating menu
                var $clonedMenu = $submenu.clone();
                $floatingMenu.append($clonedMenu);
            }

            // Add the floating menu to the body
            $('body').append($floatingMenu);

            // Handle click events instead of hover
            $this.on('click', function(e) {
                // Only show floating menu when sidebar is collapsed
                if ($('body').hasClass('sidebar-collapse')) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Hide all other floating menus first
                    $('.floating-menu').css('display', 'none');

                    // Calculate the position relative to the sidebar
                    // This ensures the menu stays aligned with the sidebar item even when scrolling
                    var sidebarTop = $('.main-sidebar').offset().top;
                    var itemTop = $this.position().top;
                    var scrollTop = $(window).scrollTop();

                    // Toggle the floating menu visibility
                    if ($floatingMenu.css('display') === 'block') {
                        $floatingMenu.css('display', 'none').removeClass('show');
                    } else {
                        $floatingMenu.css({
                            top: itemTop + 50, // 50px is the header height
                            display: 'block'
                        }).addClass('show');
                    }
                }
            });

            // Handle clicks on floating menu items
            $floatingMenu.on('click', 'a', function(e) {
                // Allow normal link behavior for menu items
                // Menu will be hidden when page navigates
            });
        });

        // Handle non-treeview menu items that have direct links
        var directLinkItems = $('.sidebar-menu > li:not(.treeview)');

        directLinkItems.each(function() {
            var $this = $(this);
            var $link = $this.find('> a');

            // Skip if this is just a header or doesn't have a link
            if (!$link.length || $link.attr('href') === '#') {
                return;
            }

            // Create a floating tooltip for this item
            var menuId = 'floating-tooltip-' + $this.attr('class').replace(/\s+/g, '-');
            var $floatingTooltip = $('<div class="floating-menu" id="' + menuId + '"></div>');

            // Add the link text to the tooltip
            var linkText = $link.find('span').text();
            var $tooltipContent = $('<ul><li><a href="' + $link.attr('href') + '">' + linkText + '</a></li></ul>');
            $floatingTooltip.append($tooltipContent);

            // Add the floating tooltip to the body
            $('body').append($floatingTooltip);

            // Handle click events instead of hover for direct links
            $this.on('click', function(e) {
                // Only show floating tooltip when sidebar is collapsed
                if ($('body').hasClass('sidebar-collapse')) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Hide all other floating menus first
                    $('.floating-menu').css('display', 'none');

                    // Calculate the position relative to the sidebar
                    // This ensures the tooltip stays aligned with the sidebar item even when scrolling
                    var sidebarTop = $('.main-sidebar').offset().top;
                    var itemTop = $this.position().top;
                    var scrollTop = $(window).scrollTop();

                    // Toggle the floating tooltip visibility
                    if ($floatingTooltip.css('display') === 'block') {
                        $floatingTooltip.css('display', 'none').removeClass('show');
                    } else {
                        $floatingTooltip.css({
                            top: itemTop + 50, // 50px is the header height
                            display: 'block'
                        }).addClass('show');
                    }
                }
            });

            // Handle clicks on floating tooltip items
            $floatingTooltip.on('click', 'a', function(e) {
                // Allow normal link behavior for tooltip items
                // Tooltip will be hidden when page navigates
            });
        });
    }

    // Initialize floating menus
    initFloatingMenus();

    // Reinitialize floating menus when sidebar state changes
    $('body').on('collapsed.pushMenu expanded.pushMenu', function() {
        // Remove existing floating menus
        $('.floating-menu').remove();

        // Reinitialize floating menus
        initFloatingMenus();
    });

    // Update menu positions when scrolling
    $(window).on('scroll', function() {
        // Only update if sidebar is collapsed and there are visible floating menus
        if ($('body').hasClass('sidebar-collapse')) {
            // Hide all floating menus when scrolling
            $('.floating-menu').css('display', 'none').removeClass('show');
        }
    });

    // Hide floating menus when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.main-sidebar, .floating-menu').length) {
            $('.floating-menu').css('display', 'none').removeClass('show');
        }
    });

    // Prevent sidebar expansion on any hover events
    $(document).on('mouseenter mouseleave', '.main-sidebar', function(e) {
        e.stopPropagation();
        e.preventDefault();
        return false;
    });
});
